# -*- coding: utf-8 -*-
import asyncio
import argparse
import pathlib
import re
from datetime import datetime
from playwright.async_api import async_playwright, Page

START_URL = "https://gptpluscz.com/user/wholesale"
STATE_DIR = pathlib.Path("./_playwright_state")

def log(msg: str) -> None:
    ts = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{ts}] {msg}")

# ---------- table parsing ----------
async def read_stock(page: Page, goods_id: int):
    """
    Return stock (int) from the row whose first column equals goods_id.
    Return None if the row is not found.
    """
    js = """
    (targetId) => {
      const tbl = document.querySelector("table");
      if (!tbl) return null;
      const rows = tbl.querySelectorAll("tbody tr");
      for (const tr of rows) {
        const cells = tr.querySelectorAll("td");
        if (!cells.length) continue;
        const idText = (cells[0].textContent || "").trim();
        if (idText === String(targetId)) {
          const stockCell = cells[5]; // 6th column is stock
          if (!stockCell) return 0;
          const num = parseInt((stockCell.textContent || "").replace(/[^0-9]/g, ""), 10);
          return Number.isFinite(num) ? num : 0;
        }
      }
      return null;
    }
    """
    return await page.evaluate(js, goods_id)

# ---------- direct POST path ----------
async def wholesale_post(page: Page, goods_id: int, count: int):
    """
    Submit POST /user/wholesale inside the page context:
    body: _token, goods_id, count (x-www-form-urlencoded).
    """
    js = """
    async (data) => {
      const { gid, cnt } = data;

      function getToken() {
        const i = document.querySelector('input[name="_token"]');
        if (i && i.value) return i.value;
        const m = document.querySelector('meta[name="csrf-token"]');
        if (m && m.content) return m.content;
        const match = document.cookie.match(/XSRF-TOKEN=([^;]+)/);
        if (match) { try { return decodeURIComponent(match[1]); } catch(e) {} }
        return null;
      }

      const token = getToken();
      if (!token) return { ok: false, status: 0, text: "csrf token not found" };

      const params = new URLSearchParams();
      params.set("_token", token);
      params.set("goods_id", String(gid));
      params.set("count", String(cnt));

      const resp = await fetch("/user/wholesale", {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: params.toString(),
        credentials: "include"
      });

      const text = await resp.text();
      return { ok: resp.ok, status: resp.status, text: text.slice(0, 8000) };
    }
    """
    return await page.evaluate(js, {"gid": goods_id, "cnt": count})

# ---------- robust UI fallback ----------
async def ui_buy(page: Page, goods_id: int, count: int):
    """
    Click '批发' in the row -> wait dialog -> fill count -> click submit.
    Return True if submit button was clicked.
    """
    # click the '批发' button in the row
    js_click_row = """
    (gid) => {
      const rows = document.querySelectorAll('table tbody tr');
      for (const tr of rows) {
        const tds = tr.querySelectorAll('td');
        if (!tds.length) continue;
        const idText = (tds[0].textContent || "").trim();
        if (idText === String(gid)) {
          tr.scrollIntoView({block:'center', behavior:'instant'});
          const btn = Array.from(tr.querySelectorAll('button,a,[role=button],.btn'))
            .find(b => /批发|wholesale/i.test((b.textContent || "").trim()));
          if (btn) { btn.click(); return true; }
        }
      }
      return false;
    }
    """
    clicked = await page.evaluate(js_click_row, goods_id)
    if not clicked:
        log("UI: row or '批发' button not found")
        return False

    # wait some dialog hint
    try:
        ok = False
        for sel in [
            "text=点击批发",
            "text=商品批发",
            "text=批发数",
            "input[placeholder*=数量]",
            "input[type=number]"
        ]:
            try:
                await page.locator(sel).first.wait_for(timeout=2500)
                ok = True
                break
            except Exception:
                pass
        if not ok:
            await page.wait_for_timeout(400)
    except Exception:
        pass

    # fill count (several strategies)
    filled = False
    for locator in [
        page.get_by_placeholder("请输入需要批发的数量"),
        page.locator("input[placeholder*=数量]").first,
        page.locator("input[type=number]").first,
        page.locator(":is(.modal,.el-dialog,.ant-modal,[role=dialog]) >> visible=true").locator("input").first,
    ]:
        try:
            await locator.wait_for(timeout=1500)
            await locator.fill(str(count))
            filled = True
            break
        except Exception:
            continue

    if not filled:
        # JS fallback to set value and fire input
        filled = await page.evaluate("""
        (val) => {
          function visible(el){
            if(!el) return false;
            const s = getComputedStyle(el);
            const r = el.getBoundingClientRect();
            return s.display!=='none' && s.visibility!=='hidden' && r.width>0 && r.height>0;
          }
          let container = null;
          const all = Array.from(document.querySelectorAll('.modal,.el-dialog,.ant-modal,[role=dialog],body'));
          for (const el of all) {
            if (visible(el) && /点击批发|商品批发|批发数/.test(el.textContent||'')) { container = el; break; }
          }
          if (!container) container = document;
          const inputs = Array.from(container.querySelectorAll('input[type=number],input,textarea'))
                               .filter(visible)
                               .filter(el => !el.readOnly && !el.disabled);
          const target = inputs[0];
          if (!target) return false;
          target.focus();
          target.value = '';
          target.dispatchEvent(new Event('input', {bubbles:true}));
          target.value = String(val);
          target.dispatchEvent(new Event('input', {bubbles:true}));
          return true;
        }
        """, count)
    if not filled:
        log("UI: cannot locate or fill count input")
        return False

    # click submit button
    clicked_submit = False
    for sel in [
        "button:has-text('点击批发')",
        "[type=submit]:has-text('点击批发')",
        ":is(.modal,.el-dialog,.ant-modal,[role=dialog]) >> text=点击批发",
        "button:has-text('批发')",
        "[role=button]:has-text('点击批发')",
        "a:has-text('点击批发')",
    ]:
        try:
            btn = page.locator(sel).first
            if await btn.is_visible():
                await btn.click()
                clicked_submit = True
                break
        except Exception:
            continue

    if not clicked_submit:
        clicked_submit = await page.evaluate("""
        () => {
          function visible(el){
            if(!el) return false;
            const s = getComputedStyle(el);
            const r = el.getBoundingClientRect();
            return s.display!=='none' && s.visibility!=='hidden' && r.width>0 && r.height>0;
          }
          const candidates = Array.from(document.querySelectorAll('button,a,[role=button],[type=submit]'))
              .filter(visible);
          const btn = candidates.find(b => /点击批发/.test((b.textContent||'').trim()))
                   || candidates.find(b => /^批发$/.test((b.textContent||'').trim()));
          if (btn) { btn.click(); return true; }
          return false;
        }
        """)
    if not clicked_submit:
        log("UI: submit button not found/clicked")
        return False

    return True

# ---------- success classification ----------
def _norm(text: str) -> str:
    return re.sub(r"\s+", " ", text or "")

SUCCESS_TOKENS = [
    "订单详情", "订单编号", "订单状态", "卡密", "已完成", "支付方式", "余额支付", "Order details"
]
FAIL_TOKENS = [
    "错误", "失败", "出现错误", "下单失败", "库存不足", "请登录", "未登录", "余额不足", "invalid", "error"
]

def classify_response(html: str) -> str:
    t = _norm(html)
    if any(k in t for k in FAIL_TOKENS):
        return "fail"
    if any(k in t for k in SUCCESS_TOKENS):
        return "success"
    return "unknown"

# ---------- purchase attempt ----------
async def attempt_purchase(page: Page, goods_id: int, count: int) -> bool:
    log("condition met, try POST first...")
    res = await wholesale_post(page, goods_id, count)
    status = res.get("status")
    body   = res.get("text", "")
    cls    = classify_response(body)
    log(f"POST result: ok={res.get('ok')} status={status} classify={cls}")
    snippet = re.sub(r"\s+", " ", body)[:220]
    if snippet:
        log(f"server snippet: {snippet}")

    if res.get("ok") and cls == "success":
        log("SUCCESS via POST")
        return True

    if cls == "fail":
        log("Server shows an error page; will NOT treat as success.")

    log("try UI fallback...")
    ui_ok = await ui_buy(page, goods_id, count)
    if ui_ok:
        await page.wait_for_timeout(1200)
        try:
            page_html = await page.content()
            if classify_response(page_html) == "success":
                log("SUCCESS via UI")
                return True
        except Exception:
            pass
        log("UI submitted but no success hint; keep polling.")
        return False
    else:
        log("UI fallback failed; keep polling.")
        return False

# ---------- main ----------
async def main():
    parser = argparse.ArgumentParser(description="gptpluscz wholesale sniper")
    parser.add_argument("--goods-id", type=int, required=True, help="ID in the first column")
    parser.add_argument("--buy", type=int, default=1, help="count to buy")
    parser.add_argument("--interval", type=float, default=1.2, help="polling interval seconds")
    parser.add_argument("--timeout", type=int, default=0, help="max minutes to run, 0=unlimited")
    parser.add_argument("--headless", action="store_true", help="run headless")
    parser.add_argument("--stay-open", action="store_true", help="成功后不关闭浏览器，等待你按回车")   #抢完后不关网页
    parser.add_argument(
        "--trigger",
        choices=["gt0", "eq0"],
        default="gt0",       # 这里改默认值：gt0=库存>0；eq0=库存==0（联调用）
        help="trigger condition: gt0 (stock>0, default) or eq0 (stock==0, for testing)",
    )
    args = parser.parse_args()

    async with async_playwright() as p:
        user_data_dir = str(STATE_DIR.resolve())
        ctx = await p.chromium.launch_persistent_context(
            user_data_dir,
            headless=args.headless,
            viewport={"width": 1280, "height": 900},
        )
        page = await ctx.new_page()
        log(f"open {START_URL}")
        await page.goto(START_URL, wait_until="domcontentloaded")

        if "login" in page.url.lower():
            log("please login in the opened browser, then press Enter here to start...")
            await asyncio.to_thread(input)

        log("start monitoring... (Ctrl+C to exit)")
        start = asyncio.get_event_loop().time()
        attempt = 0

        while True:
            attempt += 1
            try:
                if "user/wholesale" not in page.url:
                    await page.goto(START_URL, wait_until="domcontentloaded")

                stock = await read_stock(page, args.goods_id)
                if stock is None:
                    log("row not found, will refresh")
                else:
                    log(f"attempt {attempt}: stock = {stock}")
                    cond = (stock > 0) if args.trigger == "gt0" else (stock == 0)
                    if cond:
                        ok = await attempt_purchase(page, args.goods_id, args.buy)
                        if ok:
                            break

                if args.timeout and (asyncio.get_event_loop().time() - start) > args.timeout * 60:
                    log("timeout reached, exit.")
                    break

                await asyncio.sleep(max(0.3, args.interval))
                await page.reload(wait_until="domcontentloaded")
            except Exception as e:
                log(f"exception: {e.__class__.__name__}: {e}")
                await asyncio.sleep(1.0)
                try:
                    await page.reload(wait_until="domcontentloaded")
                except Exception:
                    pass


        # ---- end of while True loop ----

        # 成功后不关闭浏览器（需加 --stay-open）
        if args.stay_open:
            log("抢购完成，浏览器保持打开。按回车退出脚本...")
            try:
                await asyncio.to_thread(input)
            except Exception:
                pass

        await ctx.close()


if __name__ == "__main__":
    asyncio.run(main())
