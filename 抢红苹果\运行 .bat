@echo off
setlocal EnableExtensions EnableDelayedExpansion
chcp 65001 >NUL
title gptpluscz 批发监控 - 正式版
cd /d "%~dp0"

:: 如果你的虚拟环境不在当前目录，填这里；否则留空自动找 .venv/py/python
set "CUSTOM_VENV="
:: set "CUSTOM_VENV=D:\脚本\.venv"

set "PYEXE="
if defined CUSTOM_VENV if exist "%CUSTOM_VENV%\Scripts\python.exe" set "PYEXE=%CUSTOM_VENV%\Scripts\python.exe"
if not defined PYEXE if exist ".venv\Scripts\python.exe" set "PYEXE=.venv\Scripts\python.exe"
if not defined PYEXE ( where py >NUL 2>&1 && set "PYEXE=py -3" )
if not defined PYEXE ( where python >NUL 2>&1 && set "PYEXE=python" )
if not defined PYEXE ( echo [ERROR] 未找到 Python。& pause & exit /b 1 )

if not exist "wholesale_sniper_prod.py" (
  echo [ERROR] 未找到 wholesale_sniper_prod.py（请放在本目录）
  pause & exit /b 1
)

echo ================== 正式版参数 ==================
set /p GOODS_ID=商品ID（例如 1）: 
if "%GOODS_ID%"=="" ( echo 必须输入商品ID。 & pause & exit /b 1 )

set /p BUY=抢购数量（默认 1）: 
if "%BUY%"=="" set "BUY=1"

set /p INTERVAL=轮询间隔秒（默认 1.2）: 
if "%INTERVAL%"=="" set "INTERVAL=1.2"

set /p TIMEOUT=最大运行分钟（默认 0=不限制）: 
if "%TIMEOUT%"=="" set "TIMEOUT=0"

set /p HEADLESS=无界面模式? [y/N]: 
set "HEADFLAG="
if /I "%HEADLESS%"=="Y" set "HEADFLAG=--headless"

echo.
echo [RUN] %PYEXE% wholesale_sniper_prod.py --goods-id %GOODS_ID% --buy %BUY% --interval %INTERVAL% --timeout %TIMEOUT% %HEADFLAG%
echo.

%PYEXE% wholesale_sniper_prod.py --goods-id %GOODS_ID% --buy %BUY% --interval %INTERVAL% --timeout %TIMEOUT% %HEADFLAG%
echo.
echo ===== 程序结束，退出码=%ERRORLEVEL% =====
pause
endlocal
